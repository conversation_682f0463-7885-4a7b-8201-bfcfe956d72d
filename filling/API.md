# Filling Microservice API Documentation

## Base URL
```
http://localhost:3001/api/v1/filling
```

## Authentication
All endpoints require JWT authentication via <PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <token>
```

Admin endpoints require admin-level JWT token.

## Endpoints

### 1. Create Registration Form
**POST** `/create`

Creates a new business registration form.

**Headers:**
- `Authorization: Bearer <user_token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "businessId": "string",
  "details": {
    "businessNames": {
      "type": "string",
      "name1": "string",
      "name2": "string (optional)",
      "name3": "string (optional)",
      "phone": "string",
      "email": "string",
      "businessNature": "string"
    },
    "director": {
      "firstName": "string",
      "lastName": "string",
      "otherName": "string (optional)",
      "dateOfBirth": "string (ISO date)",
      "gender": "male|female|other",
      "nationality": "string",
      "phone": "string",
      "email": "string",
      "occupation": "string",
      "address": "string",
      "nin": "string",
      "identification": "string",
      "passport": "string",
      "signature": "string"
    },
    "witness": {
      "firstName": "string",
      "lastName": "string",
      "otherName": "string (optional)",
      "dateOfBirth": "string (ISO date)",
      "gender": "male|female|other",
      "nationality": "string",
      "phone": "string",
      "email": "string",
      "occupation": "string",
      "address": "string",
      "witnessSignature": "string (optional)"
    }
  },
  "txId": "string",
  "status": "pending|approved|rejected",
  "document": "string"
}
```

**Response:**
```json
{
  "message": "Registration form created successfully",
  "form": { ... }
}
```

### 2. Get Registration Forms by Business ID
**GET** `/:businessId`

Retrieves all registration forms for a specific business.

**Headers:**
- `Authorization: Bearer <user_token>`

**Response:**
```json
{
  "message": "Registration forms fetched successfully",
  "forms": [{ ... }]
}
```

### 3. Get All Registration Forms (Admin)
**GET** `/`

Retrieves all registration forms (admin only).

**Headers:**
- `Authorization: Bearer <admin_token>`

**Response:**
```json
{
  "message": "Registration forms fetched successfully",
  "forms": [{ ... }]
}
```

### 4. Update Registration Form (Admin)
**PATCH** `/:id`

Updates a registration form by ID (admin only).

**Headers:**
- `Authorization: Bearer <admin_token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "details": {
    "businessNames": { ... },
    "director": { ... },
    "witness": { ... }
  },
  "status": "pending|approved|rejected",
  "document": "string"
}
```

**Response:**
```json
{
  "message": "Registration form updated successfully",
  "form": { ... }
}
```

### 5. Add Files to Registration Form (Admin)
**POST** `/add-file/:id`

Adds files to a registration form (admin only).

**Headers:**
- `Authorization: Bearer <admin_token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "files": [
    {
      "title": "string",
      "url": "string (valid URL)"
    }
  ]
}
```

**Response:**
```json
{
  "message": "File details added successfully",
  "form": {
    "data": [{ ... }]
  }
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "message": "Error description"
}
```

Common HTTP status codes:
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid or missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource not found)
- `500` - Internal Server Error

## Data Models

### Registration Form
```json
{
  "_id": "string",
  "businessId": "string",
  "details": {
    "businessNames": { ... },
    "director": { ... },
    "witness": { ... }
  },
  "file": [
    {
      "title": "string",
      "url": "string"
    }
  ],
  "txId": "string",
  "status": "pending|approved|rejected",
  "document": "string",
  "createdAt": "string (ISO date)",
  "updatedAt": "string (ISO date)"
}
```
