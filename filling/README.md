# Filling Microservice

This microservice handles business registration and document management functionality that was separated from the main Coconut backend services.

## Features

- Business registration form creation
- Document file management
- Registration status tracking
- Director and witness information management
- File upload and attachment handling

## API Endpoints

### POST /api/v1/filling/create
Create a new business registration form

### GET /api/v1/filling/:businessId
Fetch registration forms by business ID

### GET /api/v1/filling/
Fetch all registration forms (admin only)

### PATCH /api/v1/filling/:id
Update a registration form (admin only)

### POST /api/v1/filling/add-file/:id
Add files to a registration form (admin only)

## Project Structure

```
filling/
├── src/
│   ├── controllers/
│   │   └── fillingRecords.ts
│   ├── models/
│   │   └── FillingRecords.ts
│   ├── routes/
│   │   └── fillingRoute.ts
│   ├── services/
│   │   └── fillingRecords.ts
│   ├── schemas/
│   │   └── fillingRequestSchema.ts
│   ├── middlewares/
│   │   ├── auth.ts
│   │   ├── validate.ts
│   │   └── errorHandler.ts
│   ├── utils/
│   │   ├── sendResponse.ts
│   │   └── envValidator.ts
│   ├── config/
│   │   ├── db.ts
│   │   └── swaggerConfig.ts
│   ├── helpers/
│   │   └── utils.ts
│   ├── app.ts
│   └── server.ts
├── package.json
├── tsconfig.json
└── README.md
```

## Installation

1. Navigate to the filling directory:
   ```bash
   cd filling
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables (copy from main project)

4. Run the service:
   ```bash
   npm run dev
   ```

## Environment Variables

The service uses the same environment variables as the main project:
- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - JWT secret for user authentication
- `ADMIN_JWT_SECRET` - JWT secret for admin authentication
- `PORT` - Port number (defaults to 3001)

## Dependencies

This microservice maintains the same dependencies as the main project but only includes what's necessary for filling operations.

## Authentication

The service uses JWT-based authentication with two levels:
- `protect` - Regular user authentication
- `adminProtect` - Admin-level authentication

## Database

Uses MongoDB with Mongoose ODM. The FillingRecords model references the Business model via `businessId`.
