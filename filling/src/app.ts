import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import connectDB from "./config/db";
import fillingRoute from "./routes/fillingRoute";
import { errorHandler } from "./middlewares/errorHandler";
import { setupSwagger } from "./config/swaggerConfig";
import { limiter } from "./helpers/utils";

const app = express();
app.set("trust proxy", 1);
app.use(morgan("dev"));
app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(limiter);

app.get("/", (req, res) => {
  res.send("Welcome to Filling Microservice API");
});

app.use("/api/v1/filling", fillingRoute);

// Setup Swagger API Docs
app.use(errorHandler);
setupSwagger(app);

connectDB();

export default app;
